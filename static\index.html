<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тренажер Аудирования по Английскому</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto py-8">
        <header class="text-center mb-12">
            <h1 class="text-4xl font-bold text-blue-600 mb-2">Тренажер Аудирования</h1>
            <p class="text-gray-600">Улучшайте навыки восприятия английского на слух с ИИ</p>
        </header>

        <div id="setup-screen" class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-2xl font-semibold mb-4">Выберите тему</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <button class="topic-btn p-4 bg-blue-100 rounded-lg hover:bg-blue-200 transition" data-topic="daily_life">
                    Повседневная жизнь
                </button>
                <button class="topic-btn p-4 bg-green-100 rounded-lg hover:bg-green-200 transition" data-topic="travel">
                    Путешествия
                </button>
                <button class="topic-btn p-4 bg-yellow-100 rounded-lg hover:bg-yellow-200 transition" data-topic="food">
                    Еда и рестораны
                </button>
                <button class="topic-btn p-4 bg-purple-100 rounded-lg hover:bg-purple-200 transition" data-topic="hobbies">
                    Хобби
                </button>
            </div>
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    Урок: 10 минут
                </div>
                <button id="start-btn" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                    Начать урок
                </button>
            </div>
        </div>

        <div id="lesson-screen" class="hidden bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-6">
                <h2 id="topic-display" class="text-2xl font-semibold"></h2>
                <div class="text-gray-600">
                    Время: <span id="timer">10:00</span>
                </div>
            </div>

            <div id="exercise-container" class="mb-6 p-4 bg-gray-50 rounded-lg">
                <div id="sentence-display" class="text-xl mb-4 text-center"></div>
                <div id="translation-container" class="hidden">
                    <p class="font-semibold mb-2">Ваш перевод:</p>
                    <div id="user-translation" class="p-3 bg-white border rounded mb-2"></div>
                    <div id="correction" class="mt-4 p-3 bg-blue-50 rounded hidden">
                        <p class="font-semibold">Исправление:</p>
                        <p id="correct-translation" class="mb-2"></p>
                        <p id="explanation" class="text-sm text-gray-600"></p>
                    </div>
                </div>
            </div>

            <div class="flex justify-between items-center">
                <div>
                    <span id="score" class="font-semibold">Счет: 0/0</span>
                </div>
                <div class="space-x-2">
                    <button id="listen-again" class="bg-gray-200 px-4 py-2 rounded-lg hover:bg-gray-300 transition">
                        Прослушать снова
                    </button>
                    <button id="check-answer" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                        Проверить ответ
                    </button>
                    <button id="next-btn" class="hidden bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition">
                        Далее
                    </button>
                </div>
            </div>
        </div>

        <div id="results-screen" class="hidden bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-semibold mb-6 text-center">Урок завершен!</h2>
            <div class="text-center mb-8">
                <div class="text-4xl font-bold text-blue-600 mb-2" id="final-score">8/10</div>
                <div class="text-gray-600 mb-6">Ваши навыки аудирования улучшаются!</div>
                
                <div class="mb-6 text-left">
                    <h3 class="font-semibold text-lg mb-2">Итоги урока:</h3>
                    <ul id="summary-list" class="list-disc pl-5 space-y-2">
                        <!-- Summary items will be added here -->
                    </ul>
                </div>

                <div class="space-x-4">
                    <button id="restart-btn" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                        Попробовать снова
                    </button>
                    <button id="new-lesson-btn" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition">
                        Новый урок
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/app.js"></script>
</body>
</html>
