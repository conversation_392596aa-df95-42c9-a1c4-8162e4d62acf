from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from pathlib import Path
import json
import os
import base64
import asyncio
from typing import Dict, List, Optional
import openai
from dotenv import load_dotenv
import random
import re
import difflib
from datetime import datetime

# Load environment variables
load_dotenv()

# Initialize OpenAI client
openai.api_key = os.getenv("OPENAI_API_KEY")
if not openai.api_key:
    raise ValueError("OPENAI_API_KEY environment variable not set")

# Reuse a single async OpenAI client across requests
openai_client = openai.AsyncOpenAI()

# Sample sentences by topic and difficulty
SENTENCES = {
    "daily_life": [
        "I wake up at 7 o'clock every morning.",
        "I usually have breakfast at home before work.",
        "My daily routine includes checking emails first thing in the morning.",
        "After work, I like to go for a walk in the park.",
        "On weekends, I enjoy spending time with my family and friends.",
    ],
    "travel": [
        "I love traveling to new countries and experiencing different cultures.",
        "The best way to explore a city is by walking around and getting lost.",
        "I always make sure to pack light when I go on a trip.",
        "The most beautiful place I've ever visited is the Amalfi Coast in Italy.",
        "Traveling by train is my favorite way to see the countryside.",
    ],
    "food": [
        "My favorite cuisine is Italian, especially pasta and pizza.",
        "I try to eat healthy by including lots of vegetables in my meals.",
        "Cooking at home is usually healthier than eating out.",
        "I love trying new recipes from different countries.",
        "Breakfast is the most important meal of the day.",
    ],
    "hobbies": [
        "In my free time, I enjoy reading books and listening to music.",
        "Photography is my passion, especially landscape photography.",
        "I've been practicing yoga for three years now.",
        "Playing a musical instrument is a great way to relax.",
        "Gardening helps me connect with nature and reduce stress.",
    ]
}

def get_random_sentence(topic: str, difficulty: int) -> str:
    """Get a sentence based on topic and difficulty.
    Logic: allow up to `difficulty` items as a pool and choose randomly from it.
    """
    if topic not in SENTENCES:
        topic = random.choice(list(SENTENCES.keys()))

    sentences = SENTENCES[topic]
    pool_size = max(1, min(difficulty, len(sentences)))
    pool = sentences[:pool_size]
    return random.choice(pool)

# --- Translation and evaluation helpers ---
async def translate_to_russian(text: str) -> str:
    """Use OpenAI to translate English text to Russian (return plain text only)."""
    try:
        resp = await openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a translation assistant. Translate the user's English sentence into natural Russian. Return only the translation without quotes."},
                {"role": "user", "content": text},
            ],
            temperature=0.2,
        )
        ru = resp.choices[0].message.content.strip()
        if (ru.startswith('"') and ru.endswith('"')) or (ru.startswith("'") and ru.endswith("'")):
            ru = ru[1:-1].strip()
        return ru
    except Exception as e:
        print(f"Error translating to Russian: {e}")
        return ""


def _normalize_ru(s: str) -> str:
    s = (s or "").lower()
    s = s.replace("ё", "е")
    s = re.sub(r"[^а-яa-z0-9\s]", " ", s)
    s = re.sub(r"\s+", " ", s).strip()
    return s


def _jaccard(tokens_a, tokens_b) -> float:
    if not tokens_a or not tokens_b:
        return 0.0
    sa, sb = set(tokens_a), set(tokens_b)
    inter = len(sa & sb)
    union = len(sa | sb)
    return (inter / union) if union else 0.0


def _char_ratio(a: str, b: str) -> float:
    return difflib.SequenceMatcher(a=a, b=b).ratio()


def evaluate_translation(student_ru: str, reference_ru: str):
    ns = _normalize_ru(student_ru)
    nr = _normalize_ru(reference_ru)
    char = _char_ratio(ns, nr)
    jac = _jaccard(ns.split(), nr.split())
    score = 0.6 * char + 0.4 * jac
    is_correct = score >= 0.72 or (char >= 0.75 and jac >= 0.5)
    return is_correct, {"char_ratio": round(char, 3), "jaccard": round(jac, 3), "score": round(score, 3)}


def generate_feedback(student_ru: str, reference_ru: str) -> str:
    ns = _normalize_ru(student_ru)
    nr = _normalize_ru(reference_ru)
    sw, rw = ns.split(), nr.split()
    missing = [w for w in rw if w not in sw]
    extra = [w for w in sw if w not in rw]
    tips = []
    if missing:
        tips.append(f"Возможно, упущены ключевые слова: {', '.join(missing[:5])}.")
    if extra:
        tips.append(f"Лишние/необязательные слова: {', '.join(extra[:5])}.")
    if not tips:
        tips.append("Перевод близок по смыслу. Обрати внимание на точность формулировок.")
    return " ".join(tips)

app = FastAPI()

# Add CORS middleware to allow requests from the frontend
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with your frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Store active connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.client_data: Dict[str, dict] = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        print(f"Client {client_id} connecting...")
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.client_data[client_id] = {
            'topic': None,
            'difficulty': 1,
            'score': {'correct': 0, 'total': 0}
        }
        print(f"Client {client_id} connected. Active connections: {len(self.active_connections)}")

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        if client_id in self.client_data:
            del self.client_data[client_id]

    async def send_json(self, client_id: str, data: dict):
        if client_id in self.active_connections:
            await self.active_connections[client_id].send_json(data)

    def update_client_data(self, client_id: str, **kwargs):
        if client_id in self.client_data:
            self.client_data[client_id].update(kwargs)

    def get_client_data(self, client_id: str) -> Optional[dict]:
        return self.client_data.get(client_id)

manager = ConnectionManager()

# WebSocket endpoint
async def generate_speech(text: str) -> str:
    """Generate speech from text using OpenAI TTS"""
    try:
        response = await openai_client.audio.speech.create(
            model="tts-1",
            voice="alloy",
            input=text,
            response_format="opus"
        )
        audio_data = await response.aread()
        return base64.b64encode(audio_data).decode('utf-8')
    except Exception as e:
        print(f"Error generating speech: {e}")
        return ""

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    print(f"\n=== WebSocket Connection Started ===")
    print(f"Client ID: {client_id}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    await manager.connect(websocket, client_id)
    try:
        while True:
            print(f"\n--- Waiting for message from client {client_id} ---")
            data = await websocket.receive_json()
            print(f"\n=== Received Message ===")
            print(f"Client: {client_id}")
            print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Message: {data}")

            message_type = data.get('type')
            print(f"Message type: {message_type}")

            if message_type == 'start_lesson':
                print("\n=== Starting Lesson ===")
                print(f"Topic: {data.get('topic')}")
                print("=====================")

            if message_type == 'start_lesson':
                topic = data.get('topic')
                manager.update_client_data(client_id, topic=topic, difficulty=1)
                # Send welcome message and first exercise
                await send_exercise(client_id, topic, 1, is_first_exercise=True)

            elif message_type == 'request_tts':
                text = data.get('text')
                if text:
                    audio_data = await generate_speech(text)
                    await manager.send_json(client_id, {
                        'type': 'tts_audio',
                        'audio': audio_data,
                        'text': text
                    })

            elif message_type == 'submit_translation':
                user_translation = data.get('translation', '').lower()
                client_data = manager.get_client_data(client_id)
                if client_data and 'current_sentence' in client_data:
                    # Ensure we have a reference Russian translation for current sentence
                    if 'current_sentence_ru' not in client_data or not client_data['current_sentence_ru']:
                        client_data['current_sentence_ru'] = await translate_to_russian(client_data['current_sentence'])

                    reference_ru = client_data.get('current_sentence_ru', '')
                    is_correct, metrics = evaluate_translation(user_translation, reference_ru)
                    feedback = generate_feedback(user_translation, reference_ru)

                    # Update score
                    if is_correct:
                        client_data['score']['correct'] += 1
                    client_data['score']['total'] += 1

                    # Increase difficulty if answer was correct
                    if is_correct and client_data['difficulty'] < 5:  # Max difficulty
                        client_data['difficulty'] += 1

                    await manager.send_json(client_id, {
                        'type': 'evaluation',
                        'is_correct': is_correct,
                        'correct_translation': reference_ru,
                        'metrics': metrics,
                        'feedback': feedback,
                        'score': client_data['score']
                    })

            elif message_type == 'next_exercise':
                client_data = manager.get_client_data(client_id)
                if client_data:
                    await send_exercise(client_id, client_data['topic'], client_data['difficulty'])

    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except Exception as e:
        print(f"WebSocket error: {e}")
        manager.disconnect(client_id)

async def send_exercise(client_id: str, topic: str, difficulty: int, is_first_exercise: bool = False):
    """Send a new exercise to the client"""
    if client_id not in manager.active_connections:
        return

    if is_first_exercise:
        # Send welcome message for the first exercise (text first for instant UI feedback)
        welcome_message = f"Привет! Давай начнём урок на тему '{topic.replace('_', ' ').title()}'. Я произнесу предложение на английском — переведи его на русский."
        await manager.send_json(client_id, {
            'type': 'welcome',
            'message': welcome_message,
            'audio': "",
            'topic': topic
        })
        # Generate and send TTS in background when ready
        async def _send_welcome_tts():
            audio = await generate_speech(welcome_message)
            if client_id in manager.active_connections and audio:
                await manager.send_json(client_id, {
                    'type': 'tts_audio',
                    'audio': audio,
                    'text': welcome_message
                })
        asyncio.create_task(_send_welcome_tts())
        # Small delay before sending the first exercise (give user time to read welcome)
        await asyncio.sleep(3)

    # Get a random sentence for the current topic and difficulty
    sentence = get_random_sentence(topic, difficulty)

    # Generate speech for the sentence (EN)
    audio_data = await generate_speech(sentence)

    # Translate sentence to Russian once and store for evaluation
    ru_translation = await translate_to_russian(sentence)

    # Store the current sentence and its Russian reference
    manager.client_data[client_id]['current_sentence'] = sentence
    manager.client_data[client_id]['current_sentence_ru'] = ru_translation

    # Send the exercise to the client
    await manager.send_json(client_id, {
        'type': 'exercise',
        'sentence': sentence,
        'audio': audio_data,
        'difficulty': difficulty
    })

# Serve the main page
@app.get("/", response_class=HTMLResponse)
async def get():
    with open("static/index.html", "r", encoding="utf-8") as f:
        return f.read()

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "ok", "service": "english-listening-tutor"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
