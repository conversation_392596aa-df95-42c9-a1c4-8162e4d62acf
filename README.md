# English Listening Tutor

An AI-powered web application for practicing English listening comprehension through interactive exercises with voice recognition and feedback.

## Features

- Voice-based interaction (TTS and ASR)
- Multiple topics to choose from
- Adaptive difficulty based on performance
- Real-time feedback and corrections
- Progress tracking
- 10-minute lesson format

## Setup

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Set up environment variables in a `.env` file:
   ```
   OPENAI_API_KEY=your_openai_api_key
   ```

## Running the Application

1. Start the FastAPI server:
   ```
   uvicorn main:app --reload
   ```
2. Open your browser and navigate to `http://localhost:8000`

## Technologies Used

- Backend: FastAPI, Python
- Frontend: HTML, CSS (Tailwind), JavaScript
- WebSockets for real-time communication
- OpenAI API for TTS and ASR
- Web Audio API for audio processing

## Project Structure

- `main.py` - FastAPI application and WebSocket handlers
- `static/` - Frontend assets
  - `index.html` - Main application page
  - `js/app.js` - Frontend application logic
- `requirements.txt` - Python dependencies

## License

MIT
