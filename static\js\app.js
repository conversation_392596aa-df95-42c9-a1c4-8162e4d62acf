class EnglishTutor {
    constructor() {
        this.websocket = null;
        this.audioContext = null;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.recording = false;
        this.selectedTopic = null;
        this.currentExercise = null;
        this.score = { correct: 0, total: 0 };
        this.lessonHistory = [];
        this.lessonTime = 10 * 60; // 10 minutes in seconds
        this.timerInterval = null;
        
        this.initializeElements();
        this.initializeEventListeners();
    }

    initializeElements() {
        this.elements = {
            setupScreen: document.getElementById('setup-screen'),
            lessonScreen: document.getElementById('lesson-screen'),
            resultsScreen: document.getElementById('results-screen'),
            topicDisplay: document.getElementById('topic-display'),
            sentenceDisplay: document.getElementById('sentence-display'),
            userTranslation: document.getElementById('user-translation'),
            translationContainer: document.getElementById('translation-container'),
            correction: document.getElementById('correction'),
            correctTranslation: document.getElementById('correct-translation'),
            explanation: document.getElementById('explanation'),
            score: document.getElementById('score'),
            timer: document.getElementById('timer'),
            finalScore: document.getElementById('final-score'),
            summaryList: document.getElementById('summary-list'),
            startButton: document.getElementById('start-btn'),
            listenAgainButton: document.getElementById('listen-again'),
            checkAnswerButton: document.getElementById('check-answer'),
            nextButton: document.getElementById('next-btn'),
            restartButton: document.getElementById('restart-btn'),
            newLessonButton: document.getElementById('new-lesson-btn'),
            topicButtons: document.querySelectorAll('.topic-btn')
        };
    }

    initializeEventListeners() {
        console.log('Initializing event listeners...');
        
        // Topic selection
        this.elements.topicButtons.forEach(button => {
            console.log('Adding click listener to button:', button.dataset.topic);
            button.addEventListener('click', (event) => {
                console.log('Topic button clicked:', event.target.dataset.topic);
                this.selectTopic(button);
            });
        });

        // Start lesson
        console.log('Adding click listener to start button');
        this.elements.startButton.addEventListener('click', async (event) => {
            console.log('Start lesson button clicked, selectedTopic:', this.selectedTopic);
            event.preventDefault();
            await this.startLesson();
        });

        // Lesson controls
        this.elements.listenAgainButton.addEventListener('click', () => this.playCurrentSentence());
        this.elements.checkAnswerButton.addEventListener('click', () => this.checkAnswer());
        this.elements.nextButton.addEventListener('click', () => this.nextExercise());
        
        // Results screen
        this.elements.restartButton.addEventListener('click', () => this.restartLesson());
        this.elements.newLessonButton.addEventListener('click', () => this.newLesson());

        // Initialize audio context on user interaction
        document.addEventListener('click', () => {
            if (!this.audioContext) {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            }
        }, { once: true });
    }

    selectTopic(button) {
        console.log('selectTopic called with button:', button);
        
        // Remove selection from all buttons
        this.elements.topicButtons.forEach(btn => {
            btn.classList.remove('ring-2', 'ring-blue-500');
            console.log('Removed selection from button:', btn.dataset.topic);
        });
        
        // Add selection to clicked button
        button.classList.add('ring-2', 'ring-blue-500');
        this.selectedTopic = button.dataset.topic;
        console.log('Selected topic set to:', this.selectedTopic);
        
        // Enable start button
        this.elements.startButton.disabled = false;
        console.log('Start button enabled:', !this.elements.startButton.disabled);
    }

    async startLesson() {
        console.log('\n=== Start Lesson Button Clicked ===');
        console.log('Time:', new Date().toISOString());
        console.log('Selected topic:', this.selectedTopic);
        
        if (!this.selectedTopic) {
            console.error('No topic selected!');
            alert('Пожалуйста, выберите тему');
            return;
        }

        // Disable start button to prevent multiple clicks
        this.elements.startButton.disabled = true;
        this.elements.startButton.textContent = 'Подключение...';
        
        try {
            // Close existing WebSocket connection if any
            if (this.websocket) {
                console.log('Closing existing WebSocket connection...');
                this.websocket.close();
            }
            
            // Initialize new WebSocket connection
            console.log('Initializing new WebSocket connection...');
            this.initializeWebSocket();
            
            // Wait for WebSocket to be ready with timeout
            const maxAttempts = 30; // 3 seconds max wait time (100ms * 30)
            let attempts = 0;
            
            await new Promise((resolve, reject) => {
                const checkConnection = setInterval(() => {
                    attempts++;
                    console.log(`WebSocket readyState check #${attempts}:`, 
                              this.websocket ? this.getWebSocketStateName(this.websocket.readyState) : 'no WebSocket');
                    
                    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                        console.log('WebSocket connection established successfully');
                        clearInterval(checkConnection);
                        resolve();
                    } else if (attempts >= maxAttempts) {
                        clearInterval(checkConnection);
                        reject(new Error('Не удалось установить соединение с сервером'));
                    }
                }, 100);
            });
            
            console.log('WebSocket ready state after connection:', 
                       this.getWebSocketStateName(this.websocket.readyState));
            
        } catch (error) {
            console.error('Error initializing WebSocket:', error);
            alert(error.message || 'Произошла ошибка при подключении');
            this.elements.startButton.disabled = false;
            this.elements.startButton.textContent = 'Начать урок';
            return;
        }

        // Show loading state
        this.elements.setupScreen.classList.add('hidden');
        this.elements.lessonScreen.classList.remove('hidden');
        this.elements.resultsScreen.classList.add('hidden');
        
        // Set topic display
        const topicName = this.formatTopicName(this.selectedTopic);
        this.elements.topicDisplay.textContent = topicName;

        // Reset UI
        this.score = { correct: 0, total: 0 };
        this.elements.score.textContent = 'Счет: 0/0';
        this.elements.translationContainer.classList.add('hidden');
        this.elements.correction.classList.add('hidden');
        this.elements.nextButton.classList.add('hidden');
        this.elements.checkAnswerButton.classList.remove('hidden');

        // Start lesson timer (10 minutes)
        this.startTimer(10 * 60);

        // Request first exercise
        const message = {
            type: 'start_lesson',
            topic: this.selectedTopic
        };
        
        console.log('Sending WebSocket message:', message);
        this.sendWebSocketMessage(message);
    }

    initializeWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
        const wsUrl = `${protocol}${window.location.host}/ws/${Date.now()}`;
        
        console.log('\n=== Initializing WebSocket Connection ===');
        console.log('WebSocket URL:', wsUrl);
        console.log('Time:', new Date().toISOString());
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            console.log('\n=== WebSocket Connected ===');
            console.log('Time:', new Date().toISOString());
            console.log('readyState:', this.getWebSocketStateName(this.websocket.readyState));
            console.log('Protocol:', this.websocket.protocol);
            console.log('URL:', this.websocket.url);
        };
        
        this.websocket.onerror = (error) => {
            console.error('\n=== WebSocket Error ===');
            console.error('Time:', new Date().toISOString());
            console.error('Error:', error);
            console.error('readyState:', this.getWebSocketStateName(this.websocket.readyState));
        };
        
        this.websocket.onclose = (event) => {
            console.log('\n=== WebSocket Closed ===');
            console.log('Time:', new Date().toISOString());
            console.log('Code:', event.code);
            console.log('Reason:', event.reason);
            console.log('wasClean:', event.wasClean);
        };
        
        this.websocket.onmessage = (event) => {
            console.log('\n=== WebSocket Message Received ===');
            console.log('Time:', new Date().toISOString());
            console.log('Raw data:', event.data);
            
            try {
                const data = JSON.parse(event.data);
                console.log('Parsed message:', data);
                this.handleWebSocketMessage(data);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };
        
        this.websocket.onclose = () => {
            console.log('WebSocket disconnected');
        };
        
        this.websocket.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }

    sendWebSocketMessage(message) {
        console.log('\n=== Sending WebSocket Message ===');
        console.log('Time:', new Date().toISOString());
        console.log('Message:', message);
        
        if (!this.websocket) {
            console.error('ERROR: WebSocket is not initialized!');
            return false;
        }
        
        const state = this.websocket.readyState;
        const stateName = this.getWebSocketStateName(state);
        console.log('WebSocket state:', stateName, `(${state})`);
        
        if (state === WebSocket.OPEN) {
            try {
                const messageString = JSON.stringify(message);
                console.log('Stringified message:', messageString);
                
                this.websocket.send(messageString);
                console.log('✅ Message sent successfully');
                return true;
            } catch (error) {
                console.error('❌ Error sending WebSocket message:', error);
                console.error('Error details:', {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                });
                return false;
            }
        } else {
            console.error(`❌ WebSocket is not in OPEN state. Current state: ${stateName} (${state})`);
            console.log('WebSocket object:', this.websocket);
            return false;
        }
    }
    
    getWebSocketStateName(state) {
        switch(state) {
            case WebSocket.CONNECTING: return 'CONNECTING';
            case WebSocket.OPEN: return 'OPEN';
            case WebSocket.CLOSING: return 'CLOSING';
            case WebSocket.CLOSED: return 'CLOSED';
            default: return `UNKNOWN (${state})`;
        }
    }

    handleWebSocketMessage(data) {
        console.log('Received message:', data);
        
        switch (data.type) {
            case 'welcome':
                // Display welcome message immediately; play audio only if present
                if (data && typeof data.message === 'string') {
                    this.elements.sentenceDisplay.textContent = data.message;
                }
                if (data && data.audio) {
                    this.playAudio(data.audio);
                }
                break;

            case 'exercise':
                this.handleExercise(data);
                break;
                
            case 'evaluation':
                this.handleEvaluation(data);
                break;
                
            case 'tts_audio':
                this.playAudio(data.audio);
                break;
                
            default:
                console.warn('Unknown message type:', data.type);
        }
    }

    handleExercise(exercise) {
        this.currentExercise = exercise;
        this.elements.sentenceDisplay.textContent = exercise.sentence;
        
        // Play the audio automatically
        this.playAudio(exercise.audio);
        
        // Start recording user's voice
        this.startRecording();
    }

    handleEvaluation(evaluation) {
        this.elements.translationContainer.classList.remove('hidden');
        this.elements.checkAnswerButton.classList.add('hidden');
        this.elements.nextButton.classList.remove('hidden');
        
        if (evaluation.is_correct) {
            this.elements.correction.classList.add('hidden');
        } else {
            this.elements.correction.classList.remove('hidden');
            this.elements.correctTranslation.textContent = evaluation.correct_translation;
            this.elements.explanation.textContent = 'Попробуйте еще раз';
        }
        
        // Update score
        this.elements.score.textContent = `Счет: ${evaluation.score.correct}/${evaluation.score.total}`;
    }

    async playAudio(base64Audio) {
        try {
            const audioData = Uint8Array.from(atob(base64Audio), c => c.charCodeAt(0));
            const audioBuffer = await this.audioContext.decodeAudioData(audioData.buffer);
            
            const source = this.audioContext.createBufferSource();
            source.buffer = audioBuffer;
            source.connect(this.audioContext.destination);
            source.start();
        } catch (error) {
            console.error('Error playing audio:', error);
        }
    }

    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];
            
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };
            
            this.mediaRecorder.onstop = async () => {
                const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
                const formData = new FormData();
                formData.append('audio', audioBlob, 'recording.wav');
                
                try {
                    const response = await fetch('/transcribe', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    this.handleTranscription(result);
                } catch (error) {
                    console.error('Error transcribing audio:', error);
                }
            };
            
            this.mediaRecorder.start();
            this.recording = true;
            
            // Stop recording after 10 seconds
            setTimeout(() => {
                if (this.recording) {
                    this.stopRecording();
                }
            }, 10000);
            
        } catch (error) {
            console.error('Error accessing microphone:', error);
            alert('Не удалось получить доступ к микрофону. Пожалуйста, проверьте настройки разрешений.');
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.recording) {
            this.mediaRecorder.stop();
            this.recording = false;
            
            // Stop all tracks
            if (this.mediaRecorder.stream) {
                this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            }
        }
    }

    handleTranscription(transcription) {
        if (transcription.text) {
            this.elements.userTranslation.textContent = transcription.text;
            
            // Send translation for evaluation
            this.sendWebSocketMessage({
                type: 'submit_translation',
                translation: transcription.text
            });
        } else {
            alert('Не удалось распознать речь. Пожалуйста, попробуйте еще раз.');
            this.startRecording();
        }
    }

    playCurrentSentence() {
        if (this.currentExercise && this.currentExercise.audio) {
            this.playAudio(this.currentExercise.audio);
        }
    }

    checkAnswer() {
        this.stopRecording();
    }

    nextExercise() {
        this.elements.translationContainer.classList.add('hidden');
        this.elements.correction.classList.add('hidden');
        this.elements.nextButton.classList.add('hidden');
        this.elements.checkAnswerButton.classList.remove('hidden');
        
        this.sendWebSocketMessage({
            type: 'next_exercise'
        });
    }

    startTimer(durationInSeconds) {
        let timer = durationInSeconds;
        const updateTimer = () => {
            const minutes = Math.floor(timer / 60);
            const seconds = timer % 60;
            
            this.elements.timer.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            if (--timer < 0) {
                clearInterval(timerInterval);
                this.endLesson();
            }
        };
        
        updateTimer();
        const timerInterval = setInterval(updateTimer, 1000);
        this.timerInterval = timerInterval;
    }

    endLesson() {
        this.stopRecording();
        
        // Show results screen
        this.elements.lessonScreen.classList.add('hidden');
        this.elements.resultsScreen.classList.remove('hidden');
        
        // Update final score
        const scoreText = this.elements.score.textContent.replace('Счет: ', '');
        this.elements.finalScore.textContent = scoreText;
    }

    restartLesson() {
        this.elements.resultsScreen.classList.add('hidden');
        this.audioChunks = [];
        this.startLesson();
    }

    newLesson() {
        this.elements.resultsScreen.classList.add('hidden');
        this.elements.setupScreen.classList.remove('hidden');
        
        // Close WebSocket connection if it's open
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }
    }

    formatTopicName(topic) {
        return topic.split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }
}

// Initialize the app when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.tutor = new EnglishTutor();
    
    // Check for Web Audio API support
    if (!window.AudioContext && !window.webkitAudioContext) {
        alert('Ваш браузер не поддерживает Web Audio API. Пожалуйста, используйте современный браузер.');
    }
    
    // Check for WebSocket support
    if (!window.WebSocket) {
        alert('Ваш браузер не поддерживает WebSockets. Пожалуйста, используйте современный браузер.');
    }
    
    // Check for microphone access
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        alert('Ваш браузер не поддерживает запись с микрофона. Пожалуйста, используйте современный браузер.');
    }
});
